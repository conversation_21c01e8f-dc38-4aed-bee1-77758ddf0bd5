package io.gigsta.data.repository

import io.gigsta.data.datasource.ResumeApiService
import io.gigsta.domain.model.ResumeInfo
import io.gigsta.domain.repository.ResumeRepository

class ResumeRepositoryImpl(
    private val resumeApiService: ResumeApiService
) : ResumeRepository {
    
    override suspend fun uploadResume(
        fileData: ByteArray,
        fileName: String,
        mimeType: String
    ): Result<ResumeInfo> {
        return resumeApiService.uploadResume(fileData, fileName, mimeType)
    }
    
    override suspend fun getExistingResume(): Result<ResumeInfo?> {
        return resumeApiService.getExistingResume()
    }
    
    override suspend fun deleteResume(): Result<Unit> {
        return resumeApiService.deleteResume()
    }
    
    override suspend fun getResumeUrl(resumeInfo: ResumeInfo): Result<String> {
        return try {
            val url = resumeInfo.publicUrl
            if (url != null) {
                Result.success(url)
            } else {
                Result.failure(Exception("Resume URL not available"))
            }
        } catch (e: Exception) {
            Result.failure(Exception("Failed to get resume URL: ${e.message}"))
        }
    }
}
