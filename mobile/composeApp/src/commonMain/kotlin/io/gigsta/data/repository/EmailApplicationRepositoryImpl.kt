package io.gigsta.data.repository

import io.gigsta.data.datasource.EmailApiService
import io.gigsta.domain.model.EmailApplication
import io.gigsta.domain.model.JobInfo
import io.gigsta.domain.model.ResumeInfo
import io.gigsta.domain.repository.EmailApplicationRepository

class EmailApplicationRepositoryImpl(
    private val emailApiService: EmailApiService
) : EmailApplicationRepository {
    
    override suspend fun generateEmailApplication(
        resumeInfo: ResumeInfo,
        jobInfo: JobInfo
    ): Result<EmailApplication> {
        return try {
            val result = emailApiService.generateEmailApplication(resumeInfo, jobInfo)
            
            result.fold(
                onSuccess = { response ->
                    if (response.success && response.emailApplication != null) {
                        Result.success(
                            EmailApplication(
                                subject = response.emailApplication.subject,
                                body = response.emailApplication.body,
                                emailId = response.emailId
                            )
                        )
                    } else {
                        Result.failure(Exception(response.error ?: "Unknown error occurred"))
                    }
                },
                onFailure = { exception ->
                    Result.failure(exception)
                }
            )
        } catch (e: Exception) {
            Result.failure(Exception("Failed to generate email application: ${e.message}"))
        }
    }
}
