package io.gigsta.data.datasource

import io.gigsta.data.model.EmailApplicationResponse
import io.gigsta.data.network.NetworkClient
import io.gigsta.data.network.NetworkConfig
import io.gigsta.domain.model.ResumeInfo
import io.gigsta.domain.model.JobInfo
import io.ktor.client.call.*
import io.ktor.client.request.forms.*
import io.ktor.http.*

class EmailApiService {
    
    private val httpClient = NetworkClient.client
    
    suspend fun generateEmailApplication(
        resumeInfo: ResumeInfo,
        jobInfo: JobInfo
    ): Result<EmailApplicationResponse> {
        return try {
            println("Generating email application:")
            println("- Resume file: ${resumeInfo.fileName}")
            println("- Has file data: ${resumeInfo.fileData != null}")
            println("- Job description: ${jobInfo.description?.take(50)}...")
            println("- Has job image: ${jobInfo.imageData != null}")
            val formDataList = formData {
                // Add job description if provided
                jobInfo.description?.let { description ->
                    append("jobDescription", description)
                }

                // Add job image if provided
                jobInfo.imageData?.let { imageData ->
                    append("jobImage", imageData, Headers.build {
                        append(HttpHeaders.ContentType, jobInfo.imageMimeType ?: "image/jpeg")
                        append(HttpHeaders.ContentDisposition, "filename=\"job_image.jpg\"")
                    })
                }

                // For unauthenticated resume (when fileData is available)
                resumeInfo.fileData?.let { fileData ->
                    append("unauthenticatedResumeFile", fileData, Headers.build {
                        append(HttpHeaders.ContentType, resumeInfo.mimeType)
                        append(HttpHeaders.ContentDisposition, "filename=\"${resumeInfo.fileName}\"")
                    })
                    append("unauthenticatedResumeFileName", resumeInfo.fileName)
                }
            }

            val response = httpClient.submitFormWithBinaryData(
                url = "${NetworkConfig.API_BASE_URL}/api/generate-email-application",
                formData = formDataList
            )
            
            if (response.status.isSuccess()) {
                val emailResponse = response.body<EmailApplicationResponse>()
                println("Email generation successful: ${emailResponse.success}")
                Result.success(emailResponse)
            } else {
                val errorBody = response.body<String>()
                println("API Error: ${response.status.value} - $errorBody")
                Result.failure(Exception("API Error: ${response.status.value} - $errorBody"))
            }
            
        } catch (e: Exception) {
            println("Error generating email application: ${e.message}")
            Result.failure(Exception("Failed to generate email application: ${e.message}"))
        }
    }
    
    private fun getMimeTypeFromFileName(fileName: String?): String {
        return when (fileName?.lowercase()?.substringAfterLast('.')) {
            "pdf" -> "application/pdf"
            "docx" -> "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            "doc" -> "application/msword"
            "txt" -> "text/plain"
            "png" -> "image/png"
            "jpg", "jpeg" -> "image/jpeg"
            else -> "application/octet-stream"
        }
    }
}
