package io.gigsta.data.datasource

import io.gigsta.data.network.SupabaseClient
import io.gigsta.domain.model.ResumeInfo
import io.github.jan.supabase.auth.auth
import io.github.jan.supabase.postgrest.from
import io.github.jan.supabase.storage.storage
import kotlinx.serialization.Serializable

@Serializable
data class ProfileData(
    val id: String,
    val resume_file_name: String? = null,
    val real_file_name: String? = null,
    val resume_url: String? = null,
    val resume_uploaded_at: String? = null
)

class ResumeApiService {
    
    private val supabase = SupabaseClient.client
    private val auth = supabase.auth
    private val storage = supabase.storage
    
    suspend fun uploadResume(
        fileData: ByteArray,
        fileName: String,
        mimeType: String
    ): Result<ResumeInfo> {
        return try {
            // Check if user is authenticated
            val user = auth.currentUserOrNull()
                ?: return Result.failure(Exception("User not authenticated"))
            
            // Validate file type
            val allowedTypes = listOf(
                "application/pdf",
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                "application/msword"
            )
            if (!allowedTypes.contains(mimeType)) {
                return Result.failure(Exception("Format file tidak didukung. Harap unggah file PDF atau DOCX"))
            }
            
            // Validate file size (5MB max)
            if (fileData.size > 5 * 1024 * 1024) {
                return Result.failure(Exception("Ukuran file terlalu besar. Maksimal 5MB"))
            }
            
            // Get file extension
            val fileExtension = fileName.substringAfterLast('.', "")
            val storageFileName = "${user.id}.$fileExtension"
            
            // Upload file to Supabase storage
            val uploadResult = storage
                .from("resumes")
                .upload(storageFileName, fileData)
            
            // Get public URL
            val publicUrl = storage
                .from("resumes")
                .publicUrl(storageFileName)
            
            // Update profiles table
            val currentTime = kotlinx.datetime.Clock.System.now().toString()
            val profileData = ProfileData(
                id = user.id,
                resume_file_name = storageFileName,
                real_file_name = fileName,
                resume_url = publicUrl,
                resume_uploaded_at = currentTime
            )
            
            supabase
                .from("profiles")
                .upsert(profileData)
            
            // Return ResumeInfo
            val resumeInfo = ResumeInfo(
                fileName = fileName,
                fileData = fileData,
                mimeType = mimeType,
                uploadedAt = currentTime,
                publicUrl = publicUrl
            )
            
            Result.success(resumeInfo)
            
        } catch (e: Exception) {
            println("Error uploading resume: ${e.message}")
            Result.failure(Exception("Gagal mengunggah resume: ${e.message}"))
        }
    }
    
    suspend fun getExistingResume(): Result<ResumeInfo?> {
        return try {
            // Check if user is authenticated
            val user = auth.currentUserOrNull()
                ?: return Result.failure(Exception("User not authenticated"))
            
            // Get user's resume info from profiles table
            val profile = supabase
                .from("profiles")
                .select {
                    filter {
                        eq("id", user.id)
                    }
                }
                .decodeSingleOrNull<ProfileData>()
            
            if (profile?.resume_file_name != null && profile.resume_url != null) {
                val resumeInfo = ResumeInfo(
                    fileName = profile.real_file_name ?: profile.resume_file_name,
                    fileData = null, // We don't store the actual file data for existing resumes
                    mimeType = getMimeTypeFromFileName(profile.real_file_name ?: profile.resume_file_name),
                    uploadedAt = profile.resume_uploaded_at,
                    publicUrl = profile.resume_url
                )
                Result.success(resumeInfo)
            } else {
                Result.success(null)
            }
            
        } catch (e: Exception) {
            println("Error fetching existing resume: ${e.message}")
            Result.failure(Exception("Gagal mengambil resume yang ada: ${e.message}"))
        }
    }
    
    suspend fun deleteResume(): Result<Unit> {
        return try {
            // Check if user is authenticated
            val user = auth.currentUserOrNull()
                ?: return Result.failure(Exception("User not authenticated"))
            
            // Get current resume info
            val profile = supabase
                .from("profiles")
                .select {
                    filter {
                        eq("id", user.id)
                    }
                }
                .decodeSingleOrNull<ProfileData>()
            
            // Delete file from storage if it exists
            profile?.resume_file_name?.let { fileName ->
                try {
                    storage
                        .from("resumes")
                        .delete(fileName)
                } catch (e: Exception) {
                    println("Warning: Could not delete file from storage: ${e.message}")
                    // Continue with profile update even if file deletion fails
                }
            }
            
            // Update profiles table to remove resume information
            val updatedProfile = ProfileData(
                id = user.id,
                resume_file_name = null,
                real_file_name = null,
                resume_url = null,
                resume_uploaded_at = null
            )
            
            supabase
                .from("profiles")
                .update(updatedProfile) {
                    filter {
                        eq("id", user.id)
                    }
                }
            
            Result.success(Unit)
            
        } catch (e: Exception) {
            println("Error deleting resume: ${e.message}")
            Result.failure(Exception("Gagal menghapus resume: ${e.message}"))
        }
    }
    
    private fun getMimeTypeFromFileName(fileName: String): String {
        return when (fileName.lowercase().substringAfterLast('.')) {
            "pdf" -> "application/pdf"
            "docx" -> "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            "doc" -> "application/msword"
            else -> "application/octet-stream"
        }
    }
}
