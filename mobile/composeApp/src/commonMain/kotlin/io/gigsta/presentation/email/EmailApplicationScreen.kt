package io.gigsta.presentation.email

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import io.gigsta.presentation.email.components.*
import io.gigsta.presentation.theme.Spacing
import io.gigsta.utils.rememberUrlOpener

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EmailApplicationScreen(
    onNavigateBack: () -> Unit,
    onNavigateToResult: () -> Unit,
    viewModel: EmailApplicationViewModel = viewModel(),
    modifier: Modifier = Modifier
) {
    val uiState = viewModel.uiState
    val scrollState = rememberScrollState()
    val urlOpener = rememberUrlOpener()

    // Navigate to result screen when email generation starts
    LaunchedEffect(uiState.isGenerating) {
        if (uiState.isGenerating) {
            onNavigateToResult()
        }
    }

    // Handle opening resume URL
    LaunchedEffect(uiState.shouldOpenResumeUrl, uiState.resumeUrl) {
        if (uiState.shouldOpenResumeUrl && uiState.resumeUrl != null) {
            urlOpener.openUrl(uiState.resumeUrl)
            viewModel.onResumeUrlOpened()
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "Buat Email Lamaran",
                        fontWeight = FontWeight.SemiBold
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Kembali"
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.surface,
                    titleContentColor = MaterialTheme.colorScheme.onSurface
                )
            )
        }
    ) { paddingValues ->
        Column(
            modifier = modifier
                .fillMaxSize()
                .padding(paddingValues)
                .verticalScroll(scrollState)
                .padding(Spacing.medium),
            verticalArrangement = Arrangement.spacedBy(Spacing.large)
        ) {
            // Error message
            uiState.error?.let { error ->
                Card(
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.errorContainer
                    ),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        text = error,
                        color = MaterialTheme.colorScheme.onErrorContainer,
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.padding(Spacing.medium)
                    )
                }
            }

            // Resume Upload Section
            ResumeUploadSection(
                isLoading = uiState.isResumeLoading || uiState.isResumeUploading,
                uploadSuccess = uiState.isResumeUploaded,
                existingResume = uiState.resumeInfo,
                error = if (uiState.error?.contains("resume", ignoreCase = true) == true) uiState.error else null,
                onFileSelected = { fileData, fileName, mimeType ->
                    viewModel.onResumeUploaded(fileData, fileName, mimeType)
                },
                onViewResume = viewModel::onViewResume,
                onDeleteResume = viewModel::onDeleteResume,
                isDeleting = uiState.isResumeDeleting,
                isGettingUrl = uiState.isGettingResumeUrl
            )

            // Job Info Section
            JobInfoInputSection(
                inputMethod = uiState.inputMethod,
                jobDescription = uiState.jobDescription,
                onJobDescriptionChange = viewModel::onJobDescriptionChanged,
                onInputMethodChange = viewModel::onInputMethodChanged,
                onImageSelected = { imageData, fileName, mimeType ->
                    viewModel.onJobImageSelected(imageData, fileName, mimeType)
                },
                hasJobImage = uiState.hasJobImage,
                error = if (uiState.error?.contains("lowongan") == true ||
                            uiState.error?.contains("gambar") == true ||
                            uiState.error?.contains("deskripsi") == true) uiState.error else null
            )

            // Generate Button
            val canGenerate = uiState.isResumeUploaded && when (uiState.inputMethod) {
                io.gigsta.domain.model.InputMethod.TEXT -> uiState.jobDescription.isNotBlank()
                io.gigsta.domain.model.InputMethod.IMAGE -> uiState.hasJobImage
            }

            Button(
                onClick = viewModel::generateEmail,
                modifier = Modifier.fillMaxWidth(),
                enabled = canGenerate && !uiState.isGenerating
            ) {
                if (uiState.isGenerating) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        strokeWidth = 2.dp,
                        color = MaterialTheme.colorScheme.onPrimary
                    )
                    Spacer(modifier = Modifier.width(Spacing.small))
                    Text("Membuat Email...")
                } else {
                    Text("Buat Email Lamaran")
                }
            }

            // Bottom padding for better scrolling
            Spacer(modifier = Modifier.height(Spacing.large))
        }
    }
}


