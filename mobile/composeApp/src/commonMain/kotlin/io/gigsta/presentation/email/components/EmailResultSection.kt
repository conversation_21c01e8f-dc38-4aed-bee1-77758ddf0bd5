package io.gigsta.presentation.email.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ContentCopy
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Save
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import io.gigsta.domain.model.EmailApplication
import io.gigsta.presentation.theme.Spacing

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EmailResultSection(
    emailApplication: EmailApplication?,
    isGenerating: <PERSON><PERSON><PERSON>,
    onRegenerateEmail: () -> Unit,
    modifier: Modifier = Modifier
) {
    val clipboardManager = LocalClipboardManager.current
    var isSubjectEditable by remember { mutableStateOf(false) }
    var isBodyEditable by remember { mutableStateOf(false) }
    var editableSubject by remember { mutableStateOf("") }
    var editableBody by remember { mutableStateOf("") }
    
    // Update editable content when email application changes
    LaunchedEffect(emailApplication) {
        emailApplication?.let {
            editableSubject = it.subject
            editableBody = it.body
            isSubjectEditable = false
            isBodyEditable = false
        }
    }
    
    Column(
        modifier = modifier
            .fillMaxWidth()
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.spacedBy(Spacing.medium)
    ) {
        Text(
            text = "Email Lamaran",
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.SemiBold
        )
        
        if (isGenerating) {
            GeneratingEmailCard()
        } else if (emailApplication != null) {
            // Subject section
            EmailFieldCard(
                title = "Subjek Email",
                content = editableSubject,
                isEditable = isSubjectEditable,
                onContentChange = { editableSubject = it },
                onEditToggle = { isSubjectEditable = !isSubjectEditable },
                onCopy = {
                    clipboardManager.setText(AnnotatedString(editableSubject))
                }
            )
            
            // Body section
            EmailFieldCard(
                title = "Isi Email",
                content = editableBody,
                isEditable = isBodyEditable,
                onContentChange = { editableBody = it },
                onEditToggle = { isBodyEditable = !isBodyEditable },
                onCopy = {
                    clipboardManager.setText(AnnotatedString(editableBody))
                },
                isMultiline = true
            )
            
            // Action buttons
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(Spacing.medium)
            ) {
                OutlinedButton(
                    onClick = onRegenerateEmail,
                    modifier = Modifier.weight(1f)
                ) {
                    Text("Buat Ulang")
                }
                
                Button(
                    onClick = {
                        // Copy both subject and body to clipboard
                        val fullEmail = "Subjek: $editableSubject\n\n$editableBody"
                        clipboardManager.setText(AnnotatedString(fullEmail))
                    },
                    modifier = Modifier.weight(1f)
                ) {
                    Icon(
                        imageVector = Icons.Default.ContentCopy,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(Spacing.extraSmall))
                    Text("Salin Semua")
                }
            }
        }
    }
}

@Composable
private fun GeneratingEmailCard(
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Spacing.large),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(Spacing.medium)
        ) {
            CircularProgressIndicator(
                modifier = Modifier.size(48.dp),
                color = MaterialTheme.colorScheme.primary
            )
            
            Text(
                text = "Membuat Email Lamaran...",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium
            )
            
            Text(
                text = "AI sedang menganalisis resume dan informasi lowongan untuk membuat email yang dipersonalisasi",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun EmailFieldCard(
    title: String,
    content: String,
    isEditable: Boolean,
    onContentChange: (String) -> Unit,
    onEditToggle: () -> Unit,
    onCopy: () -> Unit,
    isMultiline: Boolean = false,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Spacing.medium)
        ) {
            // Header with title and action buttons
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.primary
                )
                
                Row(
                    horizontalArrangement = Arrangement.spacedBy(Spacing.extraSmall)
                ) {
                    IconButton(
                        onClick = onCopy,
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.ContentCopy,
                            contentDescription = "Salin",
                            modifier = Modifier.size(16.dp)
                        )
                    }
                    
                    IconButton(
                        onClick = onEditToggle,
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            imageVector = if (isEditable) Icons.Default.Save else Icons.Default.Edit,
                            contentDescription = if (isEditable) "Simpan" else "Edit",
                            modifier = Modifier.size(16.dp)
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(Spacing.small))
            
            // Content
            if (isEditable) {
                OutlinedTextField(
                    value = content,
                    onValueChange = onContentChange,
                    modifier = Modifier.fillMaxWidth(),
                    minLines = if (isMultiline) 5 else 1,
                    maxLines = if (isMultiline) 15 else 3
                )
            } else {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surfaceVariant
                    ),
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Text(
                        text = content,
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.padding(Spacing.medium)
                    )
                }
            }
        }
    }
}
