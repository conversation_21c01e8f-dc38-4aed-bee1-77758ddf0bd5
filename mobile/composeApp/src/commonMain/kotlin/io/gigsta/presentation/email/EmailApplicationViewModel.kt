package io.gigsta.presentation.email

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import io.gigsta.domain.model.*
import io.gigsta.domain.usecase.GenerateEmailApplicationUseCase
import io.gigsta.domain.usecase.UploadResumeUseCase
import io.gigsta.domain.usecase.GetExistingResumeUseCase
import io.gigsta.domain.usecase.DeleteResumeUseCase
import io.gigsta.domain.usecase.GetResumeUrlUseCase
import kotlinx.coroutines.launch

class EmailApplicationViewModel(
    private val generateEmailApplicationUseCase: GenerateEmailApplicationUseCase,
    private val uploadResumeUseCase: UploadResumeUseCase,
    private val getExistingResumeUseCase: GetExistingResumeUseCase,
    private val deleteResumeUseCase: DeleteResumeUseCase,
    private val getResumeUrlUseCase: GetResumeUrlUseCase
) : ViewModel() {
    
    var uiState by mutableStateOf(EmailApplicationUiState())
        private set

    init {
        loadExistingResume()
    }
    
    fun onStepChanged(step: EmailGenerationStep) {
        uiState = uiState.copy(currentStep = step)
    }
    
    fun onInputMethodChanged(inputMethod: InputMethod) {
        uiState = uiState.copy(
            inputMethod = inputMethod,
            error = null
        )
    }
    
    fun onJobDescriptionChanged(description: String) {
        uiState = uiState.copy(
            jobDescription = description,
            error = null
        )
    }
    
    fun onResumeUploaded(fileData: ByteArray, fileName: String, mimeType: String) {
        uiState = uiState.copy(
            isResumeUploading = true,
            error = null
        )

        viewModelScope.launch {
            try {
                val result = uploadResumeUseCase(fileData, fileName, mimeType)

                result.fold(
                    onSuccess = { resumeInfo ->
                        uiState = uiState.copy(
                            resumeInfo = resumeInfo,
                            isResumeUploaded = true,
                            isResumeUploading = false,
                            error = null
                        )
                    },
                    onFailure = { exception ->
                        uiState = uiState.copy(
                            isResumeUploading = false,
                            error = exception.message ?: "Gagal mengunggah resume"
                        )
                    }
                )
            } catch (e: Exception) {
                uiState = uiState.copy(
                    isResumeUploading = false,
                    error = e.message ?: "Terjadi kesalahan saat mengunggah resume"
                )
            }
        }
    }
    
    fun onJobImageSelected(imageData: ByteArray, fileName: String, mimeType: String) {
        uiState = uiState.copy(
            jobImageData = imageData,
            jobImageMimeType = mimeType,
            hasJobImage = true,
            error = null
        )
    }
    
    fun onViewResume() {
        val resumeInfo = uiState.resumeInfo
        if (resumeInfo != null) {
            uiState = uiState.copy(isGettingResumeUrl = true)

            viewModelScope.launch {
                try {
                    val result = getResumeUrlUseCase(resumeInfo)

                    result.fold(
                        onSuccess = { url ->
                            uiState = uiState.copy(
                                isGettingResumeUrl = false,
                                resumeUrl = url,
                                shouldOpenResumeUrl = true
                            )
                        },
                        onFailure = { exception ->
                            uiState = uiState.copy(
                                isGettingResumeUrl = false,
                                error = exception.message ?: "Gagal membuka resume"
                            )
                        }
                    )
                } catch (e: Exception) {
                    uiState = uiState.copy(
                        isGettingResumeUrl = false,
                        error = e.message ?: "Terjadi kesalahan saat membuka resume"
                    )
                }
            }
        }
    }
    
    fun onDeleteResume() {
        uiState = uiState.copy(
            isResumeDeleting = true,
            error = null
        )

        viewModelScope.launch {
            try {
                val result = deleteResumeUseCase()

                result.fold(
                    onSuccess = {
                        uiState = uiState.copy(
                            resumeInfo = null,
                            isResumeUploaded = false,
                            isResumeDeleting = false,
                            error = null
                        )
                    },
                    onFailure = { exception ->
                        uiState = uiState.copy(
                            isResumeDeleting = false,
                            error = exception.message ?: "Gagal menghapus resume"
                        )
                    }
                )
            } catch (e: Exception) {
                uiState = uiState.copy(
                    isResumeDeleting = false,
                    error = e.message ?: "Terjadi kesalahan saat menghapus resume"
                )
            }
        }
    }
    
    fun generateEmail() {
        val resumeInfo = uiState.resumeInfo
        if (resumeInfo == null) {
            uiState = uiState.copy(error = "Harap unggah resume terlebih dahulu")
            return
        }
        
        // Validate job info based on input method
        val jobInfo = when (uiState.inputMethod) {
            InputMethod.TEXT -> {
                if (uiState.jobDescription.isBlank()) {
                    uiState = uiState.copy(error = "Harap isi deskripsi lowongan")
                    return
                }
                JobInfo(description = uiState.jobDescription)
            }
            InputMethod.IMAGE -> {
                if (uiState.jobImageData == null) {
                    uiState = uiState.copy(error = "Harap unggah gambar lowongan")
                    return
                }
                JobInfo(
                    imageData = uiState.jobImageData,
                    imageMimeType = uiState.jobImageMimeType
                )
            }
        }
        
        uiState = uiState.copy(
            isGenerating = true,
            error = null
        )
        
        viewModelScope.launch {
            try {
                val result = generateEmailApplicationUseCase(resumeInfo, jobInfo)
                
                result.fold(
                    onSuccess = { emailApplication ->
                        uiState = uiState.copy(
                            isGenerating = false,
                            emailApplication = emailApplication,
                            error = null
                        )
                    },
                    onFailure = { exception ->
                        uiState = uiState.copy(
                            isGenerating = false,
                            error = exception.message ?: "Terjadi kesalahan saat membuat email"
                        )
                    }
                )
            } catch (e: Exception) {
                uiState = uiState.copy(
                    isGenerating = false,
                    error = e.message ?: "Terjadi kesalahan saat membuat email"
                )
            }
        }
    }
    
    fun regenerateEmail() {
        generateEmail()
    }
    
    fun goToNextStep() {
        when (uiState.currentStep) {
            EmailGenerationStep.RESUME_UPLOAD -> {
                if (uiState.isResumeUploaded) {
                    uiState = uiState.copy(currentStep = EmailGenerationStep.JOB_INFO)
                } else {
                    uiState = uiState.copy(error = "Harap unggah resume terlebih dahulu")
                }
            }
            EmailGenerationStep.JOB_INFO -> {
                generateEmail()
            }
            EmailGenerationStep.RESULT -> {
                // Already at the last step
            }
        }
    }
    
    fun goToPreviousStep() {
        when (uiState.currentStep) {
            EmailGenerationStep.RESUME_UPLOAD -> {
                // Already at the first step
            }
            EmailGenerationStep.JOB_INFO -> {
                uiState = uiState.copy(currentStep = EmailGenerationStep.RESUME_UPLOAD)
            }
            EmailGenerationStep.RESULT -> {
                uiState = uiState.copy(currentStep = EmailGenerationStep.JOB_INFO)
            }
        }
    }
    
    fun clearError() {
        uiState = uiState.copy(error = null)
    }

    fun onResumeUrlOpened() {
        uiState = uiState.copy(shouldOpenResumeUrl = false)
    }

    private fun loadExistingResume() {
        uiState = uiState.copy(isResumeLoading = true)

        viewModelScope.launch {
            try {
                val result = getExistingResumeUseCase()

                result.fold(
                    onSuccess = { resumeInfo ->
                        uiState = uiState.copy(
                            resumeInfo = resumeInfo,
                            isResumeUploaded = resumeInfo != null,
                            isResumeLoading = false
                        )
                    },
                    onFailure = { exception ->
                        uiState = uiState.copy(
                            isResumeLoading = false,
                            error = exception.message ?: "Gagal memuat resume yang ada"
                        )
                    }
                )
            } catch (e: Exception) {
                uiState = uiState.copy(
                    isResumeLoading = false,
                    error = e.message ?: "Terjadi kesalahan saat memuat resume"
                )
            }
        }
    }
    
    private fun getCurrentTimestamp(): String {
        // TODO: Implement proper timestamp formatting
        return System.currentTimeMillis().toString()
    }
}

data class EmailApplicationUiState(
    val currentStep: EmailGenerationStep = EmailGenerationStep.RESUME_UPLOAD,
    val inputMethod: InputMethod = InputMethod.TEXT,
    val jobDescription: String = "",
    val jobImageData: ByteArray? = null,
    val jobImageMimeType: String? = null,
    val hasJobImage: Boolean = false,
    val resumeInfo: ResumeInfo? = null,
    val isResumeUploaded: Boolean = false,
    val isResumeLoading: Boolean = false,
    val isResumeUploading: Boolean = false,
    val isResumeDeleting: Boolean = false,
    val isGettingResumeUrl: Boolean = false,
    val resumeUrl: String? = null,
    val shouldOpenResumeUrl: Boolean = false,
    val isGenerating: Boolean = false,
    val emailApplication: EmailApplication? = null,
    val error: String? = null
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other == null || this::class != other::class) return false

        other as EmailApplicationUiState

        if (currentStep != other.currentStep) return false
        if (inputMethod != other.inputMethod) return false
        if (jobDescription != other.jobDescription) return false
        if (jobImageData != null) {
            if (other.jobImageData == null) return false
            if (!jobImageData.contentEquals(other.jobImageData)) return false
        } else if (other.jobImageData != null) return false
        if (jobImageMimeType != other.jobImageMimeType) return false
        if (hasJobImage != other.hasJobImage) return false
        if (resumeInfo != other.resumeInfo) return false
        if (isResumeUploaded != other.isResumeUploaded) return false
        if (isResumeLoading != other.isResumeLoading) return false
        if (isResumeUploading != other.isResumeUploading) return false
        if (isResumeDeleting != other.isResumeDeleting) return false
        if (isGettingResumeUrl != other.isGettingResumeUrl) return false
        if (resumeUrl != other.resumeUrl) return false
        if (shouldOpenResumeUrl != other.shouldOpenResumeUrl) return false
        if (isGenerating != other.isGenerating) return false
        if (emailApplication != other.emailApplication) return false
        if (error != other.error) return false

        return true
    }

    override fun hashCode(): Int {
        var result = currentStep.hashCode()
        result = 31 * result + inputMethod.hashCode()
        result = 31 * result + jobDescription.hashCode()
        result = 31 * result + (jobImageData?.contentHashCode() ?: 0)
        result = 31 * result + (jobImageMimeType?.hashCode() ?: 0)
        result = 31 * result + hasJobImage.hashCode()
        result = 31 * result + (resumeInfo?.hashCode() ?: 0)
        result = 31 * result + isResumeUploaded.hashCode()
        result = 31 * result + isResumeLoading.hashCode()
        result = 31 * result + isResumeUploading.hashCode()
        result = 31 * result + isResumeDeleting.hashCode()
        result = 31 * result + isGettingResumeUrl.hashCode()
        result = 31 * result + (resumeUrl?.hashCode() ?: 0)
        result = 31 * result + shouldOpenResumeUrl.hashCode()
        result = 31 * result + isGenerating.hashCode()
        result = 31 * result + (emailApplication?.hashCode() ?: 0)
        result = 31 * result + (error?.hashCode() ?: 0)
        return result
    }
}
