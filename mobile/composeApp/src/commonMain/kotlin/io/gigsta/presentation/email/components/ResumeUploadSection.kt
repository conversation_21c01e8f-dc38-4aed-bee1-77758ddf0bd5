package io.gigsta.presentation.email.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.CloudUpload
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Visibility
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import io.gigsta.domain.model.ResumeInfo
import io.gigsta.presentation.theme.Spacing
import io.gigsta.utils.rememberResumeFilePicker

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ResumeUploadSection(
    isLoading: Boolean,
    uploadSuccess: Boolean,
    existingResume: ResumeInfo?,
    error: String?,
    onFileSelected: (ByteArray, String, String) -> Unit,
    onViewResume: () -> Unit,
    onDeleteResume: () -> Unit,
    modifier: Modifier = Modifier,
    isDeleting: Boolean = false,
    isGettingUrl: Boolean = false
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(Spacing.medium)
    ) {
        Text(
            text = "1. Upload CV/Resume Anda",
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.SemiBold
        )
        
        when {
            isLoading -> {
                LoadingCard()
            }
            uploadSuccess && existingResume != null -> {
                ExistingResumeCard(
                    resumeInfo = existingResume,
                    onViewResume = onViewResume,
                    onDeleteResume = onDeleteResume,
                    isDeleting = isDeleting,
                    isGettingUrl = isGettingUrl
                )
            }
            else -> {
                UploadCard(onFileSelected = onFileSelected)
            }
        }
        
        error?.let { errorMessage ->
            Text(
                text = errorMessage,
                color = MaterialTheme.colorScheme.error,
                style = MaterialTheme.typography.bodySmall,
                modifier = Modifier.padding(start = Spacing.medium)
            )
        }
    }
}

@Composable
private fun LoadingCard(
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Spacing.large),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(Spacing.medium)
        ) {
            CircularProgressIndicator(
                modifier = Modifier.size(48.dp),
                color = MaterialTheme.colorScheme.primary
            )
            
            Text(
                text = "Memuat resume...",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

@Composable
private fun ExistingResumeCard(
    resumeInfo: ResumeInfo,
    onViewResume: () -> Unit,
    onDeleteResume: () -> Unit,
    modifier: Modifier = Modifier,
    isDeleting: Boolean = false,
    isGettingUrl: Boolean = false
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Spacing.medium)
        ) {
            // Header with success icon
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(Spacing.small)
            ) {
                Box(
                    modifier = Modifier
                        .size(32.dp)
                        .clip(RoundedCornerShape(16.dp))
                        .background(MaterialTheme.colorScheme.primary),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Check,
                        contentDescription = null,
                        tint = Color.White,
                        modifier = Modifier.size(16.dp)
                    )
                }
                
                Text(
                    text = "Resume aktif tersedia!",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.primary
                )
            }
            
            Spacer(modifier = Modifier.height(Spacing.medium))
            
            // File info
            Text(
                text = "File: ${resumeInfo.fileName}",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            resumeInfo.uploadedAt?.let { uploadedAt ->
                Text(
                    text = "Diunggah: $uploadedAt",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            Spacer(modifier = Modifier.height(Spacing.medium))
            
            // Action buttons
            Row(
                horizontalArrangement = Arrangement.spacedBy(Spacing.medium)
            ) {
                OutlinedButton(
                    onClick = onViewResume,
                    modifier = Modifier.weight(1f),
                    enabled = !isGettingUrl && !isDeleting
                ) {
                    if (isGettingUrl) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            strokeWidth = 2.dp
                        )
                    } else {
                        Icon(
                            imageVector = Icons.Default.Visibility,
                            contentDescription = null,
                            modifier = Modifier.size(16.dp)
                        )
                    }
                    Spacer(modifier = Modifier.width(Spacing.extraSmall))
                    Text(if (isGettingUrl) "Memuat..." else "Lihat Resume")
                }

                OutlinedButton(
                    onClick = onDeleteResume,
                    modifier = Modifier.weight(1f),
                    enabled = !isDeleting && !isGettingUrl,
                    colors = ButtonDefaults.outlinedButtonColors(
                        contentColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    if (isDeleting) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            strokeWidth = 2.dp,
                            color = MaterialTheme.colorScheme.error
                        )
                    } else {
                        Icon(
                            imageVector = Icons.Default.Delete,
                            contentDescription = null,
                            modifier = Modifier.size(16.dp)
                        )
                    }
                    Spacer(modifier = Modifier.width(Spacing.extraSmall))
                    Text(if (isDeleting) "Menghapus..." else "Ganti Resume")
                }
            }
        }
    }
}

@Composable
private fun UploadCard(
    onFileSelected: (ByteArray, String, String) -> Unit,
    modifier: Modifier = Modifier
) {
    var errorMessage by remember { mutableStateOf<String?>(null) }

    val pickFile = rememberResumeFilePicker(
        onFileSelected = { selectedFile ->
            onFileSelected(selectedFile.data, selectedFile.name, selectedFile.mimeType)
            errorMessage = null
        },
        onError = { error ->
            errorMessage = error
        }
    )

    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable { pickFile() },
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Spacing.large),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(Spacing.medium)
        ) {
            Icon(
                imageVector = Icons.Default.CloudUpload,
                contentDescription = null,
                modifier = Modifier.size(48.dp),
                tint = MaterialTheme.colorScheme.primary
            )

            Text(
                text = "Upload CV/Resume Anda",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium,
                textAlign = TextAlign.Center
            )

            Text(
                text = "Format yang didukung: PDF, DOCX\nMaksimal 5MB",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                textAlign = TextAlign.Center
            )

            Button(
                onClick = pickFile,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("Pilih File")
            }

            // Error message
            errorMessage?.let { error ->
                Text(
                    text = error,
                    color = MaterialTheme.colorScheme.error,
                    style = MaterialTheme.typography.bodySmall,
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}
