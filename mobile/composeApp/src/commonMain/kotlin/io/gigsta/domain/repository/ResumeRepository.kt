package io.gigsta.domain.repository

import io.gigsta.domain.model.ResumeInfo

interface ResumeRepository {
    suspend fun uploadResume(
        fileData: ByteArray,
        fileName: String,
        mimeType: String
    ): Result<ResumeInfo>
    
    suspend fun getExistingResume(): Result<ResumeInfo?>
    
    suspend fun deleteResume(): Result<Unit>
    
    suspend fun getResumeUrl(resumeInfo: ResumeInfo): Result<String>
}
