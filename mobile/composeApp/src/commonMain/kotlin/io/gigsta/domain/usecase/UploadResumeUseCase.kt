package io.gigsta.domain.usecase

import io.gigsta.domain.model.ResumeInfo
import io.gigsta.domain.repository.ResumeRepository

class UploadResumeUseCase(
    private val resumeRepository: ResumeRepository
) {
    suspend operator fun invoke(
        fileData: ByteArray,
        fileName: String,
        mimeType: String
    ): Result<ResumeInfo> {
        // Validate file data
        if (fileData.isEmpty()) {
            return Result.failure(Exception("File data cannot be empty"))
        }
        
        // Validate file name
        if (fileName.isBlank()) {
            return Result.failure(Exception("File name cannot be empty"))
        }
        
        // Validate file type
        val allowedTypes = listOf(
            "application/pdf",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/msword"
        )
        if (!allowedTypes.contains(mimeType)) {
            return Result.failure(Exception("Format file tidak didukung. Harap unggah file PDF atau DOCX"))
        }
        
        // Validate file size (5MB max)
        if (fileData.size > 5 * 1024 * 1024) {
            return Result.failure(Exception("Ukuran file terlalu besar. Maksimal 5MB"))
        }
        
        return resumeRepository.uploadResume(fileData, fileName, mimeType)
    }
}
