package io.gigsta.domain.model

data class EmailApplication(
    val subject: String,
    val body: String,
    val emailId: String? = null
)

data class JobInfo(
    val description: String? = null,
    val imageData: ByteArray? = null,
    val imageMimeType: String? = null
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other == null || this::class != other::class) return false

        other as JobInfo

        if (description != other.description) return false
        if (imageData != null) {
            if (other.imageData == null) return false
            if (!imageData.contentEquals(other.imageData)) return false
        } else if (other.imageData != null) return false
        if (imageMimeType != other.imageMimeType) return false

        return true
    }

    override fun hashCode(): Int {
        var result = description?.hashCode() ?: 0
        result = 31 * result + (imageData?.contentHashCode() ?: 0)
        result = 31 * result + (imageMimeType?.hashCode() ?: 0)
        return result
    }
}

data class ResumeInfo(
    val fileName: String,
    val fileData: ByteArray?,
    val mimeType: String,
    val uploadedAt: String? = null,
    val publicUrl: String? = null
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other == null || this::class != other::class) return false

        other as ResumeInfo

        if (fileName != other.fileName) return false
        if (fileData != null) {
            if (other.fileData == null) return false
            if (!fileData.contentEquals(other.fileData)) return false
        } else if (other.fileData != null) return false
        if (mimeType != other.mimeType) return false
        if (uploadedAt != other.uploadedAt) return false
        if (publicUrl != other.publicUrl) return false

        return true
    }

    override fun hashCode(): Int {
        var result = fileName.hashCode()
        result = 31 * result + (fileData?.contentHashCode() ?: 0)
        result = 31 * result + mimeType.hashCode()
        result = 31 * result + (uploadedAt?.hashCode() ?: 0)
        result = 31 * result + (publicUrl?.hashCode() ?: 0)
        return result
    }
}

enum class InputMethod {
    TEXT,
    IMAGE
}

enum class EmailGenerationStep {
    RESUME_UPLOAD,
    JOB_INFO,
    RESULT
}
