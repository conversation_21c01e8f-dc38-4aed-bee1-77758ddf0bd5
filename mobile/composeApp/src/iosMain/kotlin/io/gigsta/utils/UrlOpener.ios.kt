package io.gigsta.utils

import platform.Foundation.NSURL
import platform.UIKit.UIApplication

actual class UrlOpener {
    actual fun openUrl(url: String) {
        try {
            val nsUrl = NSURL.URLWithString(url)
            if (nsUrl != null && UIApplication.sharedApplication.canOpenURL(nsUrl)) {
                UIApplication.sharedApplication.openURL(nsUrl)
            }
        } catch (e: Exception) {
            println("Error opening URL: ${e.message}")
        }
    }
}
